{"hosting": {"public": "frontend/build", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "headers": [{"source": "**", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}]}], "rewrites": [{"source": "/api/**", "run": {"serviceId": "vidcompressor-api-server", "region": "us-central1"}}, {"source": "/notificationHub/**", "run": {"serviceId": "vidcompressor-api-server", "region": "us-central1"}}, {"source": "**", "destination": "/index.html"}]}, "emulators": {"firestore": {"port": 8080}, "hosting": {"port": 5000}, "ui": {"enabled": true}, "singleProjectMode": true, "pubsub": {"port": 8085}}}