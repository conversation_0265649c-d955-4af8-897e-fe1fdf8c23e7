FROM --platform=linux/amd64 mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /app

# Copy solution file and all project files for proper restore
COPY *.sln ./
COPY api-server/*.csproj ./api-server/
COPY worker-service/*.csproj ./worker-service/
COPY shared/*.csproj ./shared/

# Restore dependencies for the entire solution
# If solution file exists, use it; otherwise restore individual projects
RUN if ls *.sln 1> /dev/null 2>&1; then \
        echo "Solution file found, restoring solution..."; \
        dotnet restore; \
    else \
        echo "No solution file found, restoring individual projects..."; \
        dotnet restore shared/shared.csproj && \
        dotnet restore worker-service/worker-service.csproj; \
    fi

# Copy all source code
COPY worker-service/ ./worker-service/
COPY shared/ ./shared/

# Build and publish the worker service
WORKDIR /app/worker-service
RUN if ls /app/*.sln 1> /dev/null 2>&1; then \
        echo "Publishing with solution context..."; \
        dotnet publish -c Release -o out --no-restore; \
    else \
        echo "Publishing without solution context..."; \
        dotnet publish -c Release -o out; \
    fi

# Build runtime image
FROM --platform=linux/amd64 mcr.microsoft.com/dotnet/aspnet:8.0
WORKDIR /app

# Verify architecture and dotnet installation
RUN echo "=== Architecture Verification ===" && \
    uname -m && \
    dpkg --print-architecture && \
    file /usr/bin/dotnet && \
    dotnet --info && \
    echo "=== End Architecture Verification ==="

# Install runtime dependencies for image processing
RUN apt-get update && apt-get install -y \
    libgdiplus \
    libc6-dev \
    && rm -rf /var/lib/apt/lists/*

# Copy the published application
COPY --from=build /app/worker-service/out .

# Cloud Run expects the app to listen on the PORT environment variable
EXPOSE $PORT
ENV ASPNETCORE_URLS=http://+:$PORT
ENV ASPNETCORE_ENVIRONMENT=Production

ENTRYPOINT ["dotnet", "worker-service.dll"]
