using System.ComponentModel.DataAnnotations;

namespace VidCompressor.Models;

public class CompressionJob
{
    [Key]
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    [Required]
    public string UserId { get; set; } = string.Empty;
    
    [Required]
    public string MediaItemId { get; set; } = string.Empty;

    /// <summary>
    /// Type of media being compressed (photo or video)
    /// </summary>
    [Required]
    public MediaType MediaType { get; set; } = MediaType.Video;

    /// <summary>
    /// Base URL from PhotosPicker API for downloading the media item
    /// </summary>
    public string? BaseUrl { get; set; }
    
    [Required]
    public string Quality { get; set; } = string.Empty;
    
    /// <summary>
    /// Whether to upload the compressed video back to Google Photos
    /// </summary>
    public bool UploadToGooglePhotos { get; set; } = true;
    
    public CompressionJobStatus Status { get; set; } = CompressionJobStatus.Queued;
    
    public string? InputStoragePath { get; set; }
    
    public string? OutputStoragePath { get; set; }

    public string? TranscoderJobName { get; set; }

    /// <summary>
    /// Local file path of compressed media ready for batch upload
    /// </summary>
    public string? CompressedFilePath { get; set; }

    public string? ErrorMessage { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? StartedAt { get; set; }
    
    public DateTime? CompletedAt { get; set; }
    
    public long? OriginalSizeBytes { get; set; }
    
    public long? CompressedSizeBytes { get; set; }
    
    public double? CompressionRatio { get; set; }

    /// <summary>
    /// Original video width in pixels
    /// </summary>
    public int? OriginalWidth { get; set; }

    /// <summary>
    /// Original video height in pixels
    /// </summary>
    public int? OriginalHeight { get; set; }

    /// <summary>
    /// Serialized metadata from the original media item (JSON)
    /// </summary>
    public string? OriginalMetadata { get; set; }

    /// <summary>
    /// Original filename from Google Photos
    /// </summary>
    public string? OriginalFilename { get; set; }

    /// <summary>
    /// Google Photos URL for the original media item (productUrl from API)
    /// </summary>
    public string? GooglePhotosUrl { get; set; }

    /// <summary>
    /// Google Photos URL for the uploaded compressed media item (if uploaded)
    /// </summary>
    public string? CompressedGooglePhotosUrl { get; set; }

    /// <summary>
    /// Number of credits used for this compression job
    /// </summary>
    public int? CreditsUsed { get; set; }

    // Navigation property
    public User? User { get; set; }
}

public enum MediaType
{
    Photo,
    Video
}

public enum CompressionJobStatus
{
    Queued,
    DownloadingFromGooglePhotos,
    UploadingToStorage,
    TranscodingInProgress,      // For videos only
    CompressingImage,           // For photos only
    DownloadingFromStorage,
    ReadyForBatchUpload,        // New status: compression complete, waiting for batch upload
    UploadingToGooglePhotos,    // Only if UploadToGooglePhotos = true
    Completed,
    Failed,
    Cancelled
}

public class CompressionJobRequest
{
    public string Quality { get; set; } = "medium";

    /// <summary>
    /// Type of media being compressed (photo or video)
    /// </summary>
    public MediaType MediaType { get; set; } = MediaType.Video;

    /// <summary>
    /// Whether to upload the compressed media back to Google Photos (default: true)
    /// </summary>
    public bool UploadToGooglePhotos { get; set; } = true;

    /// <summary>
    /// Original filename from Google Photos
    /// </summary>
    public string? Filename { get; set; }

    /// <summary>
    /// Base URL from PhotosPicker API for downloading the media item
    /// </summary>
    public string? BaseUrl { get; set; }

    /// <summary>
    /// Original media width in pixels (from PhotosPicker metadata)
    /// </summary>
    public int? OriginalWidth { get; set; }

    /// <summary>
    /// Original media height in pixels (from PhotosPicker metadata)
    /// </summary>
    public int? OriginalHeight { get; set; }

    /// <summary>
    /// Google Photos URL for the original media item (productUrl from API)
    /// </summary>
    public string? GooglePhotosUrl { get; set; }
}

public class CompressionJobResponse
{
    public string JobId { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public double? CompressionRatio { get; set; }
    public string? ErrorMessage { get; set; }
}
