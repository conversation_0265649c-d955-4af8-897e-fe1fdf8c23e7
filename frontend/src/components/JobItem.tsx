import React, { useState } from 'react';
import {
  Box,
  Typography,
  LinearProgress,
  IconButton,
  Chip,
  Button,
  Collapse,
  Avatar,
  CircularProgress
} from '@mui/material';
import {
  ExpandMore,
  ExpandLess,
  Download,
  CheckCircle,
  Error,
  Photo,
  VideoLibrary,
  Schedule,
  Compress as CompressIcon,
  CloudUpload,
  Close,
  OpenInNew
} from '@mui/icons-material';
import { JobData } from './JobsPanel';

interface JobItemProps {
  job: JobData;
  mediaItem?: {
    id: string;
    filename: string;
    mimeType: string;
    baseUrl: string;
  };
  token: string | null;
  compact?: boolean; // New prop for compact display in grouped view
  onClearJob?: (jobId: string) => void;
}

const JobItem: React.FC<JobItemProps> = ({ job, mediaItem, token, compact = false, onClearJob }) => {
  const [expanded, setExpanded] = useState(false);
  const [downloading, setDownloading] = useState(false);

  const isCompleted = job.status.toLowerCase() === 'completed';
  const isFailed = job.status.toLowerCase() === 'failed';
  const isCancelled = job.status.toLowerCase() === 'cancelled';
  const isProcessing = !isCompleted && !isFailed && !isCancelled;



  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };



  const handleDownload = async () => {
    if (!token || !isCompleted) return;

    setDownloading(true);
    try {
      const response = await fetch(`/api/media/jobs/${job.jobId}/download`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `compressed_${job.filename || 'media'}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        console.error('Download failed:', response.statusText);
      }
    } catch (error) {
      console.error('Download error:', error);
    } finally {
      setDownloading(false);
    }
  };

  const handleOpenInGooglePhotos = (url: string) => {
    window.open(url, '_blank');
  };

  const getMediaPreview = () => {
    // Use the same preview endpoint as the gallery for consistency
    const mediaItemIdForPreview = mediaItem?.id || job.mediaItemId;
    const baseUrl = mediaItem?.baseUrl || job.baseUrl;

    if (mediaItemIdForPreview && token && baseUrl) {
      return (
        <Avatar
          src={`/api/videos/${mediaItemIdForPreview}/preview?width=60&height=60&crop=true&token=${encodeURIComponent(token)}&baseUrl=${encodeURIComponent(baseUrl)}`}
          sx={{
            width: 40,
            height: 40,
            borderRadius: 1,
            bgcolor: 'grey.200'
          }}
        >
          {job.mediaType === 'Video' ? (
            <VideoLibrary sx={{ fontSize: 20, color: 'text.secondary' }} />
          ) : (
            <Photo sx={{ fontSize: 20, color: 'text.secondary' }} />
          )}
        </Avatar>
      );
    }

    return (
      <Avatar
        sx={{
          width: 40,
          height: 40,
          borderRadius: 1,
          bgcolor: 'grey.200'
        }}
      >
        {job.mediaType === 'Video' ? (
          <VideoLibrary sx={{ fontSize: 20, color: 'text.secondary' }} />
        ) : (
          <Photo sx={{ fontSize: 20, color: 'text.secondary' }} />
        )}
      </Avatar>
    );
  };

  return (
    <Box sx={{ p: 1.5 }}>
      {/* Main Job Info - Compact Vertical Layout */}
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        {/* Media Preview */}
        {getMediaPreview()}

        {/* Job Details */}
        <Box sx={{ flex: 1, minWidth: 0 }}>
          {/* Top row: Status and minimal info */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mb: 0.5 }}>
            {/* Status chip - simplified */}
            {isCompleted ? (
              <Chip
                label="done"
                size="small"
                color="success"
                variant="outlined"
                sx={{ height: 18, fontSize: '0.6rem' }}
              />
            ) : isFailed ? (
              <Chip
                label="failed"
                size="small"
                color="error"
                variant="outlined"
                sx={{ height: 18, fontSize: '0.6rem' }}
              />
            ) : (
              <Chip
                label="processing"
                size="small"
                color="primary"
                variant="outlined"
                sx={{ height: 18, fontSize: '0.6rem' }}
              />
            )}

            {/* Quality indicator as chip */}
            <Chip
              label={job.quality}
              size="small"
              variant="outlined"
              sx={{
                height: 18,
                fontSize: '0.6rem',
                color: 'text.secondary',
                borderColor: 'text.disabled'
              }}
            />


          </Box>

          {/* Status Message - allow two lines */}
          <Typography
            variant="caption"
            sx={{
              color: 'text.secondary',
              fontSize: '0.7rem',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical',
              lineHeight: 1.2
            }}
            title={`${job.filename || mediaItem?.filename || 'Unknown file'} - ${job.message}`}
          >
            {job.message}
          </Typography>

          {/* Progress Bar (for active jobs) */}
          {isProcessing && (
            <LinearProgress
              variant="determinate"
              value={job.progress}
              sx={{
                height: 2,
                borderRadius: 1,
                mt: 0.5,
                backgroundColor: 'grey.200',
                '& .MuiLinearProgress-bar': {
                  borderRadius: 1
                }
              }}
            />
          )}
        </Box>

        {/* Actions */}
        <Box sx={{ display: 'flex', gap: 0.5, alignItems: 'center' }}>
          {isCompleted && (
            <IconButton
              size="small"
              onClick={handleDownload}
              disabled={downloading || !token}
              sx={{ p: 0.5 }}
              title="Download"
            >
              {downloading ? <CircularProgress size={12} /> : <Download sx={{ fontSize: 16 }} />}
            </IconButton>
          )}

          {/* Google Photos link for original image */}
          {job.googlePhotosUrl && (
            <IconButton
              size="small"
              onClick={() => handleOpenInGooglePhotos(job.googlePhotosUrl!)}
              sx={{ p: 0.5, color: 'text.secondary' }}
              title="Open original in Google Photos"
            >
              <OpenInNew sx={{ fontSize: 16 }} />
            </IconButton>
          )}

          {onClearJob && (
            <IconButton
              size="small"
              onClick={() => onClearJob(job.jobId)}
              sx={{ p: 0.5, color: 'text.secondary' }}
              title="Remove job"
            >
              <Close sx={{ fontSize: 16 }} />
            </IconButton>
          )}

          <IconButton
            size="small"
            onClick={() => setExpanded(!expanded)}
            sx={{ p: 0.5, color: 'text.secondary' }}
            title={expanded ? 'Hide details' : 'Show details'}
          >
            {expanded ? <ExpandLess sx={{ fontSize: 16 }} /> : <ExpandMore sx={{ fontSize: 16 }} />}
          </IconButton>
        </Box>
      </Box>
      
      {/* Expanded Details - Vertical Layout */}
      <Collapse in={expanded} timeout={200}>
        <Box sx={{ mt: 1.5, pt: 1.5, borderTop: 1, borderColor: 'divider', pl: 6 }}>
          {/* Filename */}
          <Box sx={{ mb: 1 }}>
            <Typography variant="caption" sx={{ fontSize: '0.65rem', fontWeight: 500, color: 'text.primary' }}>
              {job.filename || mediaItem?.filename || 'Unknown file'}
            </Typography>
          </Box>

          {/* Vertical list of details */}
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <Schedule sx={{ fontSize: 12, color: 'text.secondary' }} />
              <Typography variant="caption" sx={{ fontSize: '0.65rem', color: 'text.secondary' }}>
                {formatDate(job.createdAt)}
              </Typography>
            </Box>

            {job.completedAt && (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <CheckCircle sx={{ fontSize: 12, color: 'success.main' }} />
                <Typography variant="caption" sx={{ fontSize: '0.65rem', color: 'text.secondary' }}>
                  Completed {formatDate(job.completedAt)}
                </Typography>
              </Box>
            )}

            {job.compressionRatio && (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <CompressIcon sx={{ fontSize: 12, color: 'text.secondary' }} />
                <Typography variant="caption" sx={{ fontSize: '0.65rem', color: 'text.secondary' }}>
                  {(100-job.compressionRatio * 100).toFixed(1)}% compression
                </Typography>
              </Box>
            )}

            {job.creditsUsed && (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <CompressIcon sx={{ fontSize: 12, color: 'text.secondary' }} />
                <Typography variant="caption" sx={{ fontSize: '0.65rem', color: 'text.secondary' }}>
                  {job.creditsUsed} credits used
                </Typography>
              </Box>
            )}

            {job.uploadToGooglePhotos && (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <CloudUpload sx={{ fontSize: 12, color: 'text.secondary' }} />
                <Typography variant="caption" sx={{ fontSize: '0.65rem', color: 'text.secondary' }}>
                  Uploaded to Google Photos
                  {job.compressedGooglePhotosUrl && (
                    <Button
                      size="small"
                      onClick={() => handleOpenInGooglePhotos(job.compressedGooglePhotosUrl!)}
                      sx={{
                        ml: 0.5,
                        minWidth: 'auto',
                        p: 0.25,
                        fontSize: '0.6rem',
                        textTransform: 'none'
                      }}
                    >
                      View
                    </Button>
                  )}
                </Typography>
              </Box>
            )}

            {job.error && (
              <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 0.5 }}>
                <Error sx={{ fontSize: 12, color: 'error.main', mt: 0.1 }} />
                <Typography variant="caption" sx={{ fontSize: '0.65rem', color: 'error.main', wordBreak: 'break-word' }}>
                  {job.error}
                </Typography>
              </Box>
            )}
          </Box>
        </Box>
      </Collapse>
    </Box>
  );
};

export default JobItem;
