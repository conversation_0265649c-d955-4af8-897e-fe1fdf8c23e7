import React, { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Paper,
  Typography,
  Button,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  CheckCircle,
  CreditCard,
  Home
} from '@mui/icons-material';

interface SuccessPageProps {
  onCreditsUpdated?: () => void;
}

const SuccessPage: React.FC<SuccessPageProps> = ({ onCreditsUpdated }) => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [purchaseType, setPurchaseType] = useState<'credits' | 'subscription' | 'unknown'>('unknown');
  const [error] = useState<string | null>(null);

  useEffect(() => {
    const sessionId = searchParams.get('session_id');
    
    if (sessionId) {
      // This is likely a Stripe checkout success
      // We can determine the type based on the session or just show a generic success
      // For now, we'll assume it's credits since that's what we just implemented
      setPurchaseType('credits');
      
      // Refresh user credits
      if (onCreditsUpdated) {
        onCreditsUpdated();
      }
    } else {
      // This might be a subscription success or other type
      setPurchaseType('subscription');
    }
    
    setLoading(false);
  }, [searchParams, onCreditsUpdated]);

  const handleGoHome = () => {
    navigate('/');
  };

  const handleGoToCredits = () => {
    navigate('/add-credits');
  };

  if (loading) {
    return (
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '50vh' 
      }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Paper
        elevation={0}
        sx={{
          p: 6,
          textAlign: 'center',
          backgroundColor: 'background.paper',
          border: 1,
          borderColor: 'divider',
          borderRadius: 2,
          maxWidth: 600,
          mx: 'auto',
          mt: 4
        }}
      >
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
        <Button
          variant="contained"
          onClick={handleGoHome}
          startIcon={<Home />}
        >
          Go to Dashboard
        </Button>
      </Paper>
    );
  }

  return (
    <Paper
      elevation={0}
      sx={{
        p: 6,
        textAlign: 'center',
        backgroundColor: 'background.paper',
        border: 1,
        borderColor: 'divider',
        borderRadius: 2,
        maxWidth: 600,
        mx: 'auto',
        mt: 4
      }}
    >
      <Box sx={{ mb: 3 }}>
        <CheckCircle 
          sx={{ 
            fontSize: 64, 
            color: 'success.main',
            mb: 2
          }} 
        />
      </Box>

      {purchaseType === 'credits' ? (
        <>
          <Typography variant="h4" sx={{ color: 'success.main', fontWeight: 600, mb: 2 }}>
            Credits Added Successfully!
          </Typography>
          <Typography variant="body1" sx={{ color: 'text.secondary', mb: 4 }}>
            Your credits have been added to your account and are ready to use for photo and video compression.
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              onClick={handleGoHome}
              startIcon={<Home />}
              sx={{ textTransform: 'none' }}
            >
              Start Compressing
            </Button>
            <Button
              variant="outlined"
              onClick={handleGoToCredits}
              startIcon={<CreditCard />}
              sx={{ textTransform: 'none' }}
            >
              Buy More Credits
            </Button>
          </Box>
        </>
      ) : (
        <>
          <Typography variant="h4" sx={{ color: 'success.main', fontWeight: 600, mb: 2 }}>
            Thanks for subscribing!
          </Typography>
          <Typography variant="body1" sx={{ color: 'text.secondary', mb: 4 }}>
            You now have access to premium compression features.
          </Typography>
          <Button
            variant="contained"
            onClick={handleGoHome}
            startIcon={<Home />}
            sx={{ textTransform: 'none' }}
          >
            Go to Dashboard
          </Button>
        </>
      )}
    </Paper>
  );
};

export default SuccessPage;
