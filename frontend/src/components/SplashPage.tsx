import React, { useState } from 'react';
import {
  <PERSON>,
  Container,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  TextField,
  Paper,

  Card,
  CardContent,
  Fade,
  Chip,
  <PERSON><PERSON><PERSON>bar,
  Alert
} from '@mui/material';
import {
  PhotoSizeSelectActual,
  Security,
  AttachMoney,
  Restore,
  CloudUpload,
  CheckCircle,
  ArrowForward
} from '@mui/icons-material';
import ThemeA<PERSON><PERSON>ogo from './ThemeAwareLogo';
import GoogleSignInButton from './GoogleSignInButton';

interface SplashPageProps {
  onSignIn?: () => void;
}

const SplashPage: React.FC<SplashPageProps> = ({ onSignIn }) => {
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [notification, setNotification] = useState<{
    message: string;
    severity: 'success' | 'error';
  } | null>(null);

  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email || !email.includes('@')) {
      setNotification({
        message: 'Please enter a valid email address',
        severity: 'error'
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch('/api/emailsignup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email })
      });

      if (response.ok) {
        setNotification({
          message: 'Thanks! You\'ll be notified when we launch with your 100 free credits.',
          severity: 'success'
        });
        setEmail('');
      } else {
        throw new Error('Failed to sign up');
      }
    } catch (error) {
      setNotification({
        message: 'Something went wrong. Please try again.',
        severity: 'error'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const features = [
    {
      icon: <Security sx={{ fontSize: 40, color: 'primary.main' }} />,
      title: 'Connect Securely',
      description: 'Safely link your Google Photos account. We only get temporary permission for the files you choose.'
    },
    {
      icon: <PhotoSizeSelectActual sx={{ fontSize: 40, color: 'secondary.main' }} />,
      title: 'Choose Your Action',
      description: 'Select large videos to compress or old photos to upscale with our AI tool.'
    },
    {
      icon: <CloudUpload sx={{ fontSize: 40, color: 'info.main' }} />,
      title: 'Save Space & Memories',
      description: 'We process the files and upload the new versions directly to your library. You keep control.'
    }
  ];

  const benefits = [
    {
      icon: <AttachMoney sx={{ fontSize: 32, color: 'success.main' }} />,
      title: 'Save Money',
      description: 'Stop paying for storage you don\'t need. A small one-time purchase with our credits is far cheaper than a recurring $2.99/mo Google One subscription.'
    },
    {
      icon: <Restore sx={{ fontSize: 32, color: 'warning.main' }} />,
      title: 'Restore Memories',
      description: 'Our AI upscaling breathes new life into old, blurry photos. Perfect for cherished family pictures you want to print or share.'
    },
    {
      icon: <Security sx={{ fontSize: 32, color: 'primary.main' }} />,
      title: 'Secure & Private',
      description: 'Your privacy is our priority. We never see your files, and our access is temporary and approved by you for each session.'
    }
  ];

  return (
    <Box sx={{ 
      minHeight: '100vh', 
      backgroundColor: 'background.default',
      overflow: 'hidden'
    }}>
      {/* Hero Section */}
      <Container maxWidth="lg" sx={{ pt: 8, pb: 6 }}>
        <Fade in timeout={800}>
          <Box sx={{ textAlign: 'center', mb: 8 }}>
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', mb: 4 }}>
              <ThemeAwareLogo size={64} sx={{ mr: 2 }} />
              <Typography
                variant="h3"
                component="h1"
                sx={{
                  fontWeight: 300,
                  color: 'text.primary',
                  letterSpacing: '-0.5px'
                }}
              >
                Gallery Tuner
              </Typography>
            </Box>
            
            <Typography
              variant="h2"
              component="h2"
              sx={{
                fontWeight: 600,
                color: 'text.primary',
                mb: 3,
                fontSize: { xs: '2rem', md: '3rem' },
                lineHeight: 1.2
              }}
            >
              Your Google Storage is Full.<br />
              <Box component="span" sx={{ color: 'primary.main' }}>
                Fix it Without a Monthly Fee.
              </Box>
            </Typography>

            <Typography
              variant="h6"
              sx={{
                color: 'text.secondary',
                mb: 6,
                maxWidth: 600,
                mx: 'auto',
                lineHeight: 1.6
              }}
            >
              Our tool compresses your large videos and enhances old photos, saving you space and money. 
              Get early access and avoid recurring storage fees.
            </Typography>

            {/* Email Signup Form */}
            <Paper
              elevation={3}
              sx={{
                p: 4,
                maxWidth: 500,
                mx: 'auto',
                mb: 4,
                borderRadius: 3,
                background: 'linear-gradient(135deg, rgba(26, 115, 232, 0.05) 0%, rgba(52, 168, 83, 0.05) 100%)'
              }}
            >
              <form onSubmit={handleEmailSubmit}>
                <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2, mb: 3 }}>
                  <TextField
                    fullWidth
                    variant="outlined"
                    placeholder="Enter your email address"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    disabled={isSubmitting}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2
                      }
                    }}
                  />
                  <Button
                    type="submit"
                    variant="contained"
                    size="large"
                    disabled={isSubmitting}
                    endIcon={<ArrowForward />}
                    sx={{
                      borderRadius: 2,
                      textTransform: 'none',
                      fontWeight: 600,
                      px: 4,
                      minWidth: { xs: 'auto', sm: 200 }
                    }}
                  >
                    {isSubmitting ? 'Joining...' : 'Notify Me & Claim Credits'}
                  </Button>
                </Box>
                <Chip
                  icon={<CheckCircle />}
                  label="Join the waitlist and get 100 free credits at launch!"
                  color="success"
                  variant="outlined"
                  sx={{ fontWeight: 500 }}
                />
              </form>
            </Paper>

            {/* Alternative Sign In */}
            <Box sx={{ mt: 4 }}>
              <Typography variant="body2" sx={{ color: 'text.secondary', mb: 2 }}>
                Already have access? Sign in to continue
              </Typography>
              <GoogleSignInButton
                onSuccess={onSignIn || (() => {})}
                onError={(error) => {
                  console.error('Google Sign-In error:', error);
                }}
                size="medium"
                theme="outline"
                text="signin_with"
                width={250}
              />
            </Box>
          </Box>
        </Fade>
      </Container>

      {/* How It Works Section */}
      <Box sx={{ backgroundColor: 'grey.50', py: 8 }}>
        <Container maxWidth="lg">
          <Typography
            variant="h3"
            component="h2"
            sx={{
              textAlign: 'center',
              fontWeight: 600,
              color: 'text.primary',
              mb: 6
            }}
          >
            How It Works
          </Typography>
          
          <Box sx={{
            display: 'flex',
            flexWrap: 'wrap',
            gap: 4,
            justifyContent: 'center'
          }}>
            {features.map((feature, index) => (
              <Box key={index} sx={{
                flex: { xs: '1 1 100%', md: '1 1 300px' },
                maxWidth: { xs: '100%', md: '400px' }
              }}>
                <Fade in timeout={800 + index * 200}>
                  <Card
                    elevation={2}
                    sx={{
                      height: '100%',
                      textAlign: 'center',
                      p: 3,
                      borderRadius: 3,
                      transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                      '&:hover': {
                        transform: 'translateY(-4px)',
                        boxShadow: 4
                      }
                    }}
                  >
                    <CardContent>
                      <Box sx={{ mb: 3 }}>
                        <Box
                          sx={{
                            display: 'inline-flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            width: 80,
                            height: 80,
                            borderRadius: '50%',
                            backgroundColor: 'grey.100',
                            mb: 2
                          }}
                        >
                          {feature.icon}
                        </Box>
                        <Typography
                          variant="h6"
                          component="h3"
                          sx={{ fontWeight: 600, color: 'text.primary', mb: 2 }}
                        >
                          Step {index + 1}: {feature.title}
                        </Typography>
                      </Box>
                      <Typography
                        variant="body1"
                        sx={{ color: 'text.secondary', lineHeight: 1.6 }}
                      >
                        {feature.description}
                      </Typography>
                    </CardContent>
                  </Card>
                </Fade>
              </Box>
            ))}
          </Box>
        </Container>
      </Box>

      {/* Benefits Section */}
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <Typography
          variant="h3"
          component="h2"
          sx={{
            textAlign: 'center',
            fontWeight: 600,
            color: 'text.primary',
            mb: 6
          }}
        >
          Why Choose Gallery Tuner?
        </Typography>

        <Box sx={{
          display: 'flex',
          flexWrap: 'wrap',
          gap: 4,
          justifyContent: 'center'
        }}>
          {benefits.map((benefit, index) => (
            <Box key={index} sx={{
              flex: { xs: '1 1 100%', md: '1 1 300px' },
              maxWidth: { xs: '100%', md: '400px' }
            }}>
              <Fade in timeout={1000 + index * 200}>
                <Box sx={{ textAlign: 'center', p: 2 }}>
                  <Box
                    sx={{
                      display: 'inline-flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: 64,
                      height: 64,
                      borderRadius: '50%',
                      backgroundColor: 'grey.100',
                      mb: 3
                    }}
                  >
                    {benefit.icon}
                  </Box>
                  <Typography
                    variant="h5"
                    component="h3"
                    sx={{ fontWeight: 600, color: 'text.primary', mb: 2 }}
                  >
                    {benefit.title}
                  </Typography>
                  <Typography
                    variant="body1"
                    sx={{ color: 'text.secondary', lineHeight: 1.6 }}
                  >
                    {benefit.description}
                  </Typography>
                </Box>
              </Fade>
            </Box>
          ))}
        </Box>
      </Container>

      {/* Final Call to Action */}
      <Box sx={{ backgroundColor: 'primary.main', py: 8 }}>
        <Container maxWidth="md">
          <Box sx={{ textAlign: 'center' }}>
            <Typography
              variant="h3"
              component="h2"
              sx={{
                fontWeight: 600,
                color: 'white',
                mb: 3
              }}
            >
              Ready to Free Up Your Google Photos?
            </Typography>
            <Typography
              variant="h6"
              sx={{
                color: 'rgba(255, 255, 255, 0.9)',
                mb: 6,
                lineHeight: 1.6
              }}
            >
              Join thousands of users who have already saved space and money with Gallery Tuner.
              Get notified when we launch and claim your 100 free credits.
            </Typography>

            {/* Repeat Email Signup Form */}
            <Paper
              elevation={3}
              sx={{
                p: 4,
                maxWidth: 500,
                mx: 'auto',
                borderRadius: 3
              }}
            >
              <form onSubmit={handleEmailSubmit}>
                <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2, mb: 3 }}>
                  <TextField
                    fullWidth
                    variant="outlined"
                    placeholder="Enter your email address"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    disabled={isSubmitting}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2
                      }
                    }}
                  />
                  <Button
                    type="submit"
                    variant="contained"
                    size="large"
                    disabled={isSubmitting}
                    endIcon={<ArrowForward />}
                    sx={{
                      borderRadius: 2,
                      textTransform: 'none',
                      fontWeight: 600,
                      px: 4,
                      minWidth: { xs: 'auto', sm: 200 }
                    }}
                  >
                    {isSubmitting ? 'Joining...' : 'Get Early Access'}
                  </Button>
                </Box>
                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                  No spam, ever. Unsubscribe at any time.
                </Typography>
              </form>
            </Paper>
          </Box>
        </Container>
      </Box>

      <Snackbar
        open={!!notification}
        autoHideDuration={6000}
        onClose={() => setNotification(null)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setNotification(null)}
          severity={notification?.severity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {notification?.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default SplashPage;
