steps:
  # Install Node.js dependencies for frontend
  - name: 'node:18'
    entrypoint: 'npm'
    args: ['ci']
    dir: 'frontend'
    id: 'install-frontend-deps'

  # Build frontend
  - name: 'node:18'
    entrypoint: 'npm'
    args: ['run', 'build']
    dir: 'frontend'
    waitFor: ['install-frontend-deps']
    id: 'build-frontend'

  # Build API Server
  - name: 'gcr.io/cloud-builders/docker'
    args: 
      - 'build'
      --platform linux/amd64
      - '-t'
      - 'us-east4-docker.pkg.dev/$PROJECT_ID/vidcompressor/api-server:$COMMIT_SHA'
      - '-t'
      - 'us-east4-docker.pkg.dev/$PROJECT_ID/vidcompressor/api-server:latest'
      - '-f'
      - 'api-server/Dockerfile'
      - '.'
    id: 'build-api-server'

  # Build Worker Service
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      - '-t'
      - 'us-east4-docker.pkg.dev/$PROJECT_ID/vidcompressor/worker-service:$COMMIT_SHA'
      - '-t'
      - 'us-east4-docker.pkg.dev/$PROJECT_ID/vidcompressor/worker-service:latest'
      - '-f'
      - 'worker-service/Dockerfile'
      - '.'
    id: 'build-worker-service'

  # Push API Server image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'push'
      - 'us-east4-docker.pkg.dev/$PROJECT_ID/vidcompressor/api-server:$COMMIT_SHA'
    waitFor: ['build-api-server']
    id: 'push-api-server'

  # Push Worker Service image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'push'
      - 'us-east4-docker.pkg.dev/$PROJECT_ID/vidcompressor/worker-service:$COMMIT_SHA'
    waitFor: ['build-worker-service']
    id: 'push-worker-service'

  # --- DEPLOYMENT STEPS ---
  # All deployments wait for builds and pushes to complete.

  # Deploy to Firebase Hosting
  - name: 'us-docker.pkg.dev/firebase-cli/us/firebase'
    args: ['deploy', '--only', 'hosting', '--project', '$PROJECT_ID']
    waitFor: ['build-frontend', 'push-api-server', 'push-worker-service']
    id: 'deploy-frontend'

  # Deploy API Server to Cloud Run
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'vidcompressor-api-server'
      - '--image'
      - 'us-east4-docker.pkg.dev/$PROJECT_ID/vidcompressor/api-server:$COMMIT_SHA'
      - '--region'
      - 'us-central1'
      - '--platform'
      - 'managed'
      - '--ingress' 
      - 'internal-and-cloud-load-balancing'  
      - '--service-account'
      - 'vidcompressor-app@$PROJECT_ID.iam.gserviceaccount.com'
      - '--env-vars-file'
      - 'deployment/api-server-env.yaml'
      - '--set-secrets'
      - 'Google__ClientSecret=google-client-secret-prod:latest,Stripe__SecretKey=stripe-secret-key-prod:latest,Stripe__WebhookSecret=stripe-webhook-secret-prod:latest'
      - '--memory'
      - '1Gi'
      - '--cpu'
      - '1'
      - '--min-instances'
      - '0'
      - '--max-instances'
      - '10'
      - '--timeout'
      - '300'
    waitFor: ['build-frontend', 'push-api-server', 'push-worker-service']
    id: 'deploy-api-server'

  # Deploy Worker Service to Cloud Run
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'vidcompressor-worker-service'
      - '--image'
      - 'us-east4-docker.pkg.dev/$PROJECT_ID/vidcompressor/worker-service:$COMMIT_SHA'
      - '--region'
      - 'us-central1'
      - '--platform'
      - 'managed'
      - '--ingress' 
      - 'internal-and-cloud-load-balancing'      
      - '--service-account'
      - 'vidcompressor-app@$PROJECT_ID.iam.gserviceaccount.com'
      - '--env-vars-file'
      - 'deployment/worker-service-env.yaml'
      - '--memory'
      - '2Gi'
      - '--cpu'
      - '2'
      - '--min-instances'
      - '0'
      - '--max-instances'
      - '5'
      - '--timeout'
      - '900'
    waitFor: ['build-frontend', 'push-api-server', 'push-worker-service']
    id: 'deploy-worker-service'

# Store images in Artifact Registry
images:
  - 'us-east4-docker.pkg.dev/$PROJECT_ID/vidcompressor/api-server:$COMMIT_SHA'
  - 'us-east4-docker.pkg.dev/$PROJECT_ID/vidcompressor/api-server:latest'
  - 'us-east4-docker.pkg.dev/$PROJECT_ID/vidcompressor/worker-service:$COMMIT_SHA'
  - 'us-east4-docker.pkg.dev/$PROJECT_ID/vidcompressor/worker-service:latest'

# Build options
options:
  machineType: 'E2_HIGHCPU_8'
  diskSizeGb: '100'
  logging: CLOUD_LOGGING_ONLY

# Timeout for the entire build
timeout: '1200s'