using Microsoft.AspNetCore.SignalR;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;
using System.Threading.Tasks;

namespace VidCompressor.ApiServer.Hubs;

[Authorize]
public class NotificationHub : Hub
{
    public async Task SendMessage(string user, string message)
    {
        Console.WriteLine($"[SignalR] SendMessage called: {user} - {message}");
        await Clients.All.SendAsync("ReceiveMessage", user, message);
    }

    public async Task TestConnection()
    {
        Console.WriteLine($"[SignalR] TestConnection called from {Context.ConnectionId}");
        await Clients.Caller.SendAsync("TestResponse", "Connection working!");
    }

    public override async Task OnConnectedAsync()
    {
        Console.WriteLine($"[SignalR] Client connected: {Context.ConnectionId}");
        Console.WriteLine($"[SignalR] User authenticated: {Context.User?.Identity?.IsAuthenticated}");

        // Print all claims for debugging
        if (Context.User?.Claims != null)
        {
            Console.WriteLine($"[SignalR] All claims:");
            foreach (var claim in Context.User.Claims)
            {
                Console.WriteLine($"[SignalR]   {claim.Type}: {claim.Value}");
            }
        }

        var userId = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        var userIdAlt = Context.User?.FindFirst("sub")?.Value;
        var userIdAlt2 = Context.User?.FindFirst("nameid")?.Value;

        Console.WriteLine($"[SignalR] User ID (NameIdentifier): {userId}");
        Console.WriteLine($"[SignalR] User ID (sub): {userIdAlt}");
        Console.WriteLine($"[SignalR] User ID (nameid): {userIdAlt2}");

        var finalUserId = userId ?? userIdAlt ?? userIdAlt2;

        if (!string.IsNullOrEmpty(finalUserId))
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, $"User_{finalUserId}");
            Console.WriteLine($"[SignalR] Added connection {Context.ConnectionId} to group User_{finalUserId}");
        }
        else
        {
            Console.WriteLine($"[SignalR] No user ID found for connection {Context.ConnectionId}");
            // For debugging: add to a general group so we can still receive updates
            await Groups.AddToGroupAsync(Context.ConnectionId, "AllUsers");
            Console.WriteLine($"[SignalR] Added connection {Context.ConnectionId} to AllUsers group for debugging");
        }

        await base.OnConnectedAsync();
    }

    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        Console.WriteLine($"SignalR client disconnected: {Context.ConnectionId}");
        var userId = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (!string.IsNullOrEmpty(userId))
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"User_{userId}");
            Console.WriteLine($"Removed connection {Context.ConnectionId} from group User_{userId}");
        }
        await base.OnDisconnectedAsync(exception);
    }

    // Method for worker service to send updates to specific users
    public async Task SendCompressionUpdate(string userId, object updateData)
    {
        await Clients.Group($"User_{userId}").SendAsync("CompressionStatusUpdate", updateData);
    }
}
