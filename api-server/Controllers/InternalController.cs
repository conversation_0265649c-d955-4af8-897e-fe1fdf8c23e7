using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using VidCompressor.ApiServer.Hubs;
using Microsoft.Extensions.Logging;

namespace VidCompressor.ApiServer.Controllers;

[ApiController]
[Route("api/[controller]")]
public class InternalController : ControllerBase
{
    private readonly IHubContext<NotificationHub> _hubContext;
    private readonly ILogger<InternalController> _logger;

    public InternalController(
        IHubContext<NotificationHub> hubContext,
        ILogger<InternalController> logger)
    {
        _hubContext = hubContext;
        _logger = logger;
    }

    /// <summary>
    /// Internal endpoint for worker service to send compression status updates
    /// </summary>
    [HttpPost("compression-update")]
    public async Task<IActionResult> SendCompressionUpdate([FromBody] CompressionUpdateRequest request)
    {
        try
        {
            _logger.LogInformation("Received compression update for user {UserId}, job {JobId}, status {Status}",
                request.UserId, request.JobId, request.Status);

            await _hubContext.Clients.Group($"User_{request.UserId}")
                .SendAsync("CompressionStatusUpdate", new
                {
                    jobId = request.JobId,
                    mediaItemId = request.MediaItemId,
                    status = request.Status,
                    message = request.Message,
                    progress = request.Progress
                });

            _logger.LogInformation("SignalR update sent successfully for job {JobId}", request.JobId);
            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send compression update for job {JobId}", request.JobId);
            return StatusCode(500, "Failed to send update");
        }
    }
}

public class CompressionUpdateRequest
{
    public string UserId { get; set; } = string.Empty;
    public string JobId { get; set; } = string.Empty;
    public string MediaItemId { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public int Progress { get; set; }
}
