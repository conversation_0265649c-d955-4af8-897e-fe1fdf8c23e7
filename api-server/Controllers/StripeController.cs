using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Stripe;
using VidCompressor.Services;
using VidCompressor.Models;
using VidCompressor.Data;

[Route("api/[controller]")]
public class StripeController : Controller
{
    private readonly IConfiguration _configuration;
    private readonly CreditsService _creditsService;
    private readonly ILogger<StripeController> _logger;

    public StripeController(IConfiguration configuration, CreditsService creditsService, ILogger<StripeController> logger)
    {
        _configuration = configuration;
        _creditsService = creditsService;
        _logger = logger;
    }

    [HttpPost("webhook")]
    public async Task<IActionResult> Webhook()
    {
        try
        {
            var json = await new StreamReader(HttpContext.Request.Body).ReadToEndAsync();
            var stripeEvent = EventUtility.ConstructEvent(json,
                Request.Headers["Stripe-Signature"], _configuration["Stripe:WebhookSecret"]);

            _logger.LogInformation("Received Stripe webhook: {EventType}", stripeEvent.Type);

            // Handle the event
            if (stripeEvent.Type == "checkout.session.completed")
            {
                var session = stripeEvent.Data.Object as Stripe.Checkout.Session;
                await HandleCheckoutSessionCompleted(session);
            }

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing Stripe webhook");
            return BadRequest();
        }
    }

    private async Task HandleCheckoutSessionCompleted(Stripe.Checkout.Session? session)
    {
        if (session == null)
        {
            _logger.LogWarning("Received null session in checkout.session.completed webhook");
            return;
        }

        try
        {
            // Check if this is a credit purchase (has metadata)
            if (session.Metadata != null &&
                session.Metadata.ContainsKey("user_id") &&
                session.Metadata.ContainsKey("credits"))
            {
                var userId = session.Metadata["user_id"];
                var creditsStr = session.Metadata["credits"];
                var creditPackId = session.Metadata.GetValueOrDefault("credit_pack_id", "unknown");

                if (int.TryParse(creditsStr, out var credits))
                {
                    var success = await _creditsService.AddCreditsAsync(
                        userId,
                        credits,
                        CreditTransactionType.Purchase,
                        $"Credit pack purchase ({creditPackId})",
                        session.Id);

                    if (success)
                    {
                        _logger.LogInformation("Successfully added {Credits} credits to user {UserId} for session {SessionId}",
                            credits, userId, session.Id);
                    }
                    else
                    {
                        _logger.LogError("Failed to add credits to user {UserId} for session {SessionId}",
                            userId, session.Id);
                    }
                }
                else
                {
                    _logger.LogError("Invalid credits value in session metadata: {Credits}", creditsStr);
                }
            }
            else
            {
                // Handle other types of purchases (subscriptions, etc.)
                _logger.LogInformation("Checkout session completed for non-credit purchase: {SessionId}", session.Id);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling checkout session completed for session {SessionId}", session.Id);
        }
    }
}
